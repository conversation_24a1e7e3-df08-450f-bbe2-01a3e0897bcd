interface GraphNode {
    id: number;
    playerHere?: boolean;
    encounterType?: string;
    edges?: number[];
}

export function getAllFutureNodes(graphData: GraphNode[] | string): GraphNode[] {
    if (typeof graphData === "string") {
        return [];
    }

    const startNode = graphData.find((node) => node.playerHere); // find the node with playerHere property
    const bossNode = graphData.find((node) => node.encounterType === "boss"); // find the boss node

    const availableNodes: GraphNode[] = []; // add startNode to array to include current node
    const nextNodes: (GraphNode | undefined)[] = startNode?.edges?.map((edge) => graphData.find((node) => node.id === edge)) || []; // store the adjacent nodes in another array

    while (nextNodes.length > 0) {
        // continue until all available nodes have been added
        const currentNode = nextNodes.shift(); // remove the current node from the queue

        if (!currentNode) continue; // skip if node is undefined

        if (currentNode === bossNode) {
            // stop adding nodes after the boss node
            availableNodes.push(currentNode);
            break;
        }

        if (availableNodes.indexOf(currentNode) === -1) {
            // add node if it's not already in the available array
            availableNodes.push(currentNode);
            currentNode.edges?.forEach((edge) => {
                const adjacentNode = graphData.find((node) => node.id === edge);
                if (adjacentNode && nextNodes.indexOf(adjacentNode) === -1) {
                    // prevent duplicate nodes in the queue
                    nextNodes.push(adjacentNode);
                }
            });
        }
    }

    return availableNodes; // returns an array containing all available nodes and connecting nodes until the boss node
}
